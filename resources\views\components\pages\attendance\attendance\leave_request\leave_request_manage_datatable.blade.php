@props(['datas' => $datas])

<x-tools.utilities.datapager.ajax_data_pager_utils :data="$datas" />

<div class="table-responsive">
    <table class="table" id="">
        <thead style="background-color: #fff9e5;">
            <tr>
                <th class="max-w-250p">{{ __('general.id') }}</th>
                <th class="max-w-250p">{{ __('general.full_name') }}</th>
                <th class="max-w-150p">{{ __('general.father_name') }}</th>
                <th class="max-w-250p">{{ __('general.job_title') }}</th>
                <th class="max-w-250p">{{ __('general.department') }}</th>
                <th class="max-w-150p">{{ __('general.leave_type') }}</th>
                <th class="max-w-150p">{{ __('general.fromDate') }}</th>
                <th class="max-w-150p">{{ __('general.toDate') }}</th>
                <th class="max-w-150p">{{ __('general.requested_date') }}</th>
                {{-- <th class="max-w-150p">{{ __('general.location') }}</th> --}}
                <th class="max-w-250p">{{ __('general.status') }}</th>
                <th class="max-w-100p text-center">{{ __('general.actions') }}</th>
            </tr>
        </thead>
        <tbody> 
            @forelse ($datas as $row)
                <tr>
                    <td>
                        <a href="{{ route('attendance.employee.attendance', ['year' => encrypt(getCurrentShamsiYear()), 'month' => getCurrentShamsiMonth(), 'employee_id' => encrypt($row->emp_id ), 'tab' => 'leaves']) }}"
                                target="blank" type="button" title="{{ __('general.leaves') }}" 
                        {{ $row->emp_id }} </a>
                    </td>
                    <td>{{ $row->employee_name . ' - ' . $row->employee_last_name }}</td>
                    <td>{{ $row->father_name }}</td>
                    <td class="fs-7 {{ in_array($row->emp_bast_id, [12, 11, 17]) == true ? 'bg-simi-success' : '' }} ">
                        {{ $row->job_title }}</td>
                    <td class="fs-7">{{ $row->sub_department_name }}</td>
                    <td class="fs-7">{{ $row->leave_name }}</td>
                    <td class="fs-7">{{ dateTo($row->date_from, 'shamsi', false) }}</td>
                    <td class="fs-7">{{ dateTo($row->date_to, 'shamsi', false) }}</td>
                    <td class="fs-7">{{ dateTo($row->requested_at, 'shamsi', false) }}</td>
                    <td class="fs-7"><x-pages.attendance.attendance.leave_request.leave_request_badges
                            :value="$row->status" /></td>
                    <td>
                        @php
                            $id = encrypt($row->id);
                            $userType=auth()->user()->type;
                        @endphp
                        <div class="d-flex gap-1">

                           @if ((in_array($userType,[2,5]) && $row->status==1 )|| ($userType==4 && $row->status==2 ) )
                           {{-- user type 2 department owner , 5 is chairman and 4 is hr-staff --}}
                               <span class="flex-fill">
                                <x-tools.utilities.modal.modal_button
                                    click="approveLeaveRequest('{{ $id }}')" target="approveLeaveRequest"
                                    css_class="btn btn-label-info btn-sm w-100">
                                    {{ __('general.approve_reject') }}
                                </x-tools.utilities.modal.modal_button>
                            </span>
                           @endif
                            

                            <span>

                                <x-tools.utilities.modal.modal_button click="viewLeaveRequest('{{ $id }}')"
                                    target="viewLeaveRequest" css_class="btn btn-label-info btn-sm w-100"
                                    title="{{ __('general.show') }}">
                                    <i class="fa-solid fa-eye"></i> &nbsp;
                                </x-tools.utilities.modal.modal_button>
                            </span>
                        </div>

                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="10">
                        <p class="text-center">{{ __('general.not_available') }}</p>
                    </td>
                </tr>
            @endforelse
        </tbody>
    </table>

</div>
